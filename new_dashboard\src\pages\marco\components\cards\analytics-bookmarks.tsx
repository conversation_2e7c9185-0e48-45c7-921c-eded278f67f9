import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
   DropdownMenu,
   DropdownMenuContent,
   DropdownMenuItem,
   DropdownMenuSeparator,
   DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { MdPlayCircleOutline } from 'react-icons/md';
import { PiBookmarkSimpleFill } from 'react-icons/pi';
import { LuClock3 } from 'react-icons/lu';
import { MoreHorizontal } from 'lucide-react';
import { MdDeleteOutline } from 'react-icons/md';
import { MdOutlineOfflineBolt } from 'react-icons/md';
import {
   useDeleteBookmarkMutation,
   useFetchBookmarksQuery,
   useStartAnalysisMutation,
   useUpdateBookmarkMutation,
} from '../../apis/analytics-agent-apis';
import AddPromptDialog from '../dialogs/add-prompt-dialog';
import { cn } from '@/utils';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';
import { toast } from 'sonner';
import { Bookmark, fetchJobStatus } from '@/api/service/analytis-bookmarks';
import { useEffect, useState } from 'react';
import { Spinner } from '@chakra-ui/react';
import { useAppDispatch, useAppSelector } from '@/store/store';
import { setRunningBookmarks } from '@/store/reducer/analytics-agent-reducer';
import {
   useFetchFeatureUsageQuery,
   useTrackFeatureUsageMutation,
} from '../../apis/analytics-agent-apis';

export interface Form {
   id?: string;
   prompt: string;
   mode: string;
   auto_run: boolean;
   time: string;
   days: string[];
   new_recipient: string;
   email: { email: string; valid: boolean }[];
}

const AnalyticsBookmarks = () => {
   const dispatch = useAppDispatch();

   const { client_id, user_id, email } = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   ) as AuthUser;

   const { runningBookmarks } = useAppSelector((state) => state.analyticsAgent);

   const [open, setOpen] = useState<boolean>(false);
   const [form, setForm] = useState<Form>({
      id: '',
      prompt: '',
      mode: 'data-analyst',
      auto_run: false,
      time: '09:00',
      days: [],
      new_recipient: '',
      email: [{ email, valid: true }],
   });

   const { data: bookmarks, refetch: refetchBookmarks } =
      useFetchBookmarksQuery();
   const { data: featureUsage, refetch: refetchFeatureUsage } =
      useFetchFeatureUsageQuery();

   const { mutateAsync: updatePrompt } = useUpdateBookmarkMutation();
   const { mutateAsync: deleteBookmark } = useDeleteBookmarkMutation();
   const { mutateAsync: startAnalysis } = useStartAnalysisMutation();
   const { mutateAsync: updateFeatureUsage } = useTrackFeatureUsageMutation();

   const handleEdit = (bookmark: Bookmark) => {
      setForm({
         id: bookmark.id,
         prompt: bookmark.prompt,
         mode: bookmark.mode,
         days: bookmark.days,
         time: bookmark.time,
         auto_run: bookmark.auto_run,
         new_recipient: '',
         email: bookmark.email
            .split(',')
            .map((x) => ({ email: x, valid: true })),
      });
      setOpen(true);
   };

   const handleToggleAutoRun = async (bookmark: Bookmark) => {
      if (bookmark.auto_run) {
         const payload = {
            client_id: String(client_id),
            user_id: String(user_id),
            id: bookmark.id,
            auto_run: false,
            prompt: bookmark.prompt,
            time: bookmark.time,
            days: bookmark.days,
            mode: bookmark.mode,
         };

         await updatePrompt(payload);
         await refetchBookmarks();

         return;
      }

      setForm({
         id: bookmark.id,
         prompt: bookmark.prompt,
         mode: bookmark.mode,
         days: bookmark.days,
         time: bookmark.time,
         auto_run: true,
         new_recipient: '',
         email: bookmark.email
            .split(',')
            .map((x) => ({ email: x, valid: true })),
      });
      setOpen(true);
   };

   const handleDelete = async (id: string) => {
      const payload = {
         client_id: String(client_id),
         user_id: String(user_id),
         id,
      };

      await deleteBookmark(payload);
      await refetchBookmarks();

      toast.success('Bookmark deleted successfully');
   };

   const handleStartAnalysis = async (bookmark: Bookmark) => {
      try {
         if (
            featureUsage &&
            featureUsage.length > 0 &&
            featureUsage.find((obj) => obj.mode === bookmark.mode) &&
            !featureUsage.find((obj) => obj.mode === bookmark.mode)?.is_enabled
         ) {
            toast.error(
               `Your free limit has expired for ${bookmark.mode
                  .split('-')
                  .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                  .join(
                     ' ',
                  )} mode. Please contact Flable support and upgrade to continue using the Analytics Agent.`,
            );
            return;
         }

         const payload = {
            client_id: String(client_id),
            user_id: String(user_id),
            id: bookmark.id,
            prompt: bookmark.prompt,
            time: bookmark.time,
            days: bookmark.days,
            mode: bookmark.mode,
            auto_run: bookmark.auto_run,
            email: bookmark.email,
         };

         const response = await startAnalysis(payload);
         const jobId = response.job_id;

         dispatch(setRunningBookmarks([...runningBookmarks, bookmark.id]));
         toast.success(
            'Analysis started successfully. You will receive an email soon.',
         );

         const intervalId = setInterval(() => {
            void (async () => {
               try {
                  const status = await fetchJobStatus({
                     client_id: String(client_id),
                     user_id: String(user_id),
                     id: jobId,
                  });

                  if (
                     status?.response_status === 'completed' ||
                     status?.response_status === 'failed'
                  ) {
                     clearInterval(intervalId);
                     dispatch(
                        setRunningBookmarks(
                           runningBookmarks.filter((id) => id !== bookmark.id),
                        ),
                     );

                     if (status.response_status === 'completed') {
                        toast.success('Analysis completed and email sent.');
                     } else {
                        toast.error(`Analysis failed`);
                     }
                  }
               } catch (err) {
                  console.error('Error checking job status:', err);
               }
            })();
         }, 15000);

         const updateFeatureUsagePayload = {
            client_id: client_id || '',
            user_id: user_id || '',
            feature_name: 'analytics_agent',
            feature_type: 'agent',
            mode: bookmark.mode,
         };
         await updateFeatureUsage(updateFeatureUsagePayload);
         await refetchFeatureUsage();
      } catch (err) {
         dispatch(
            setRunningBookmarks(
               runningBookmarks.filter((id) => id !== bookmark.id),
            ),
         );
         toast.error('Failed to send analysis report');
      }
   };

   useEffect(() => {
      const fetchData = async () => {
         await refetchBookmarks();
      };

      void fetchData();
   }, []);

   return (
      <div className='w-[98%] flex items-center justify-center mx-4 my-8'>
         <div className='w-[60%] flex-col items-center justify-between'>
            <div className='w-full flex items-center justify-between'>
               <div className='flex gap-1 items-center'>
                  <PiBookmarkSimpleFill size={22} />
                  <h1 className='text-xl font-semibold'>Bookmarks</h1>
               </div>
               <AddPromptDialog
                  form={form}
                  setForm={setForm}
                  open={open}
                  setOpen={setOpen}
               />
            </div>
            <div className='w-[98%] flex items-center justify-between mt-6'>
               <Input placeholder='Search Bookmarks' />
            </div>
            <div className='flex-col justify-between items-center mt-8'>
               {bookmarks &&
                  bookmarks.length > 0 &&
                  bookmarks
                     .sort((a, b) => {
                        const dateA = new Date(a.created_at);
                        const dateB = new Date(b.created_at);
                        return dateB.getTime() - dateA.getTime();
                     })
                     .map((bookmark) => (
                        <div className='h-[110px] !flex flex-col items-center justify-between m-2 border-b-2 pb-4'>
                           <div className='w-[98%] flex items-start justify-between gap-2 mt-1'>
                              <p
                                 className='text-sm overflow-hidden text-ellipsis font-semibold'
                                 style={{
                                    display: '-webkit-box',
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: 'vertical',
                                 }}
                              >
                                 {bookmark.prompt}
                              </p>
                              <DropdownMenu>
                                 <DropdownMenuTrigger>
                                    <Button
                                       variant='ghost'
                                       className='h-8 w-8 p-0 hover:bg-gray-100'
                                    >
                                       <span className='sr-only'>
                                          Open menu
                                       </span>
                                       <MoreHorizontal />
                                    </Button>
                                 </DropdownMenuTrigger>
                                 <DropdownMenuContent
                                    align='end'
                                    className='bg-white py-2'
                                 >
                                    <DropdownMenuItem
                                       className='hover:bg-gray-100 cursor-pointer'
                                       onClick={() => handleEdit(bookmark)}
                                    >
                                       <MdDeleteOutline
                                          size={10}
                                          fontSize={10}
                                          color='blue'
                                       />
                                       <p className='text-md font-semibold text-blue-600'>
                                          Edit Bookmark
                                       </p>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator className='bg-gray-200 mx-[1px]' />
                                    <DropdownMenuItem
                                       className='hover:bg-gray-100 cursor-pointer'
                                       onClick={() =>
                                          void handleStartAnalysis(bookmark)
                                       }
                                    >
                                       <MdPlayCircleOutline
                                          size={10}
                                          fontSize={10}
                                          color='green'
                                       />
                                       <p className='text-md font-semibold text-green-600'>
                                          Run Analysis
                                       </p>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator className='bg-gray-200 mx-[1px]' />
                                    <DropdownMenuItem
                                       className='hover:bg-gray-100 cursor-pointer'
                                       onClick={() =>
                                          void handleToggleAutoRun(bookmark)
                                       }
                                    >
                                       <MdOutlineOfflineBolt
                                          size={10}
                                          fontSize={10}
                                          color={
                                             bookmark.auto_run ? 'red' : 'green'
                                          }
                                       />
                                       <p
                                          className={cn(
                                             'text-md font-semibold',
                                             bookmark.auto_run
                                                ? 'text-red-600'
                                                : 'text-green-600',
                                          )}
                                       >
                                          {bookmark.auto_run
                                             ? 'Stop AutoRun'
                                             : 'Enable AutoRun'}
                                       </p>
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator className='bg-gray-200 mx-[1px]' />
                                    <DropdownMenuItem
                                       className='hover:bg-gray-100 cursor-pointer'
                                       onClick={() =>
                                          void handleDelete(bookmark.id)
                                       }
                                    >
                                       <MdDeleteOutline
                                          size={10}
                                          fontSize={10}
                                          color='red'
                                       />
                                       <p className='text-md font-semibold text-red-600'>
                                          Delete Bookmark
                                       </p>
                                    </DropdownMenuItem>
                                 </DropdownMenuContent>
                              </DropdownMenu>
                           </div>
                           <div className='w-[98%] flex items-center justify-between mt-1'>
                              <div className='flex gap-2 items-center'>
                                 <LuClock3 size={14} />
                                 <div className='text-xs font-bold text-gray-500'>
                                    {new Date(
                                       bookmark.updated_at,
                                    ).toLocaleString('en-US', {
                                       month: 'short',
                                       day: '2-digit',
                                       year: 'numeric',
                                       hour: '2-digit',
                                       minute: '2-digit',
                                       hour12: true,
                                    })}
                                 </div>
                              </div>
                              <div className='flex justify-end items-center gap-2'>
                                 {runningBookmarks.includes(bookmark.id) && (
                                    <div className='flex justify-between items-center gap-2 border-2 px-2 py-1 rounded-sm'>
                                       <span className='text-xs text-green-800'>
                                          Analysis Running
                                       </span>
                                       <Spinner size='xs' color='green' />
                                    </div>
                                 )}

                                 <div className='text-xs font-semibold border-2 rounded-sm px-2 py-1'>
                                    {bookmark.mode.toUpperCase()}
                                 </div>
                              </div>
                           </div>
                        </div>
                     ))}
            </div>
         </div>
      </div>
   );
};

export default AnalyticsBookmarks;
