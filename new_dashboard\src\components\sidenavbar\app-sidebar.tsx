import {
   Sidebar,
   SidebarContent,
   <PERSON>barFooter,
   SidebarHeader,
   SidebarTrigger,
   useSidebar,
} from '@/components/ui/sidebar';
import FlableLogoWithName from '@/assets/image/flableicon.png';
import FlableLogo from '@/assets/icons/icon.png';
import AppSidebarContent from './app-sidebar-content';
import AppSidebarFooter from './app-sidebar-footer';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { AuthUser } from '@/types/auth';
import { cn } from '@/utils';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   const { open } = useSidebar();

   const userDetails = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   return (
      <Sidebar
         className='h-[100vh] max-h-[100vh] overflow-hidden'
         collapsible='icon'
         {...props}
      >
         <SidebarHeader className='!bg-[#ffffff]'>
            <div className='flex items-center justify-between'>
               <img
                  src={open ? FlableLogoWithName : FlableLogo}
                  className={cn(
                     'mt-1',
                     open ? 'md:w-[130px]' : 'md:w-[30px]',
                     open ? 'w-[100px]' : 'w-[50px]',
                  )}
                  alt='flable icon'
               />
               <SidebarTrigger className='ml-2 hover:cursor-pointer' />
            </div>
         </SidebarHeader>
         <SidebarContent
            className={cn(open && 'px-4', '!bg-[#ffffff] flex flex-col h-full')}
         >
            <AppSidebarContent />
         </SidebarContent>
         <SidebarFooter className={cn(open ? 'px-4' : 'p-0')}>
            {userDetails?.user_role === 'Admin' && <AppSidebarFooter />}
         </SidebarFooter>
      </Sidebar>
   );
}
