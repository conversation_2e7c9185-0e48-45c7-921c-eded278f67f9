import { Box, Heading, Text, SimpleGrid, Card, CardBody, Stat, StatLabel, StatNumber, StatHelpText,Flex,useToast,useColorMode} from '@chakra-ui/react';

//import { FiUsers, FiTrendingUp, FiDollarSign, FiActivity } from 'react-icons/fi';
import kpiService from '@/api/service/kpi/index';
import { useApiQuery } from '@/hooks/react-query-hooks';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { useAppDispatch } from '../../store/store';
import { useEffect,useState} from 'react';
import DateRangeSelect from './components/date-select';
import Loading from '@/components/loading';
import { useAppSelector } from '../../store/store';
import { resetDateRange } from '../../store/reducer/kpi-reducer';
const Dashboard = () => {
   
  const { dateRange } = useAppSelector((state) => state.kpi)
const toast = useToast();
   const [initialLoadStarted, setInitialLoadStarted] = useState(false);

   const {
      data: aggTrackData,
      isFetching: trackingLoading,
      isLoading: trackingDataLoading,
      errorMessage,
      refetch: getInitialKpiData,
   } = useApiQuery({
      queryKey: ['tracking-data'],
      queryFn: () => kpiService.getTrackData(getPayload()),
      getInitialData: () => undefined,
      enabled: false,
   });

      const getPayload = () => {
      return {
         clientId: LocalStorageService.getItem(Keys.ClientId) as string,
         startDate: dateRange.start,
         endDate: dateRange.end,
      };
   };
    useEffect(() => {
      setInitialLoadStarted(true);
      getInitialKpiData().catch(console.log);
     
   }, [dateRange]);
    useEffect(() => {
      return () => {
         dispatch(resetDateRange());
      };
   }, []);
    const { colorMode } = useColorMode();
   const dispatch= useAppDispatch();
 return (
      <>
         {trackingLoading ? (
            <Loading />
         ) : (
            <Box
              
            >
               <Flex direction={'column'} gap={5}>
                  <Flex justifyContent={'space-between'}>
                     <Flex alignItems='center'>
                        <Heading
                           fontSize={24}
                           size={'xl'}
                           fontWeight={600}
                           color={colorMode === 'dark' ? 'white' : 'inherit'}
                        >
                           Dashboard
                        </Heading>
                        
                     </Flex>
                     <DateRangeSelect  />
                  </Flex>
                  <Flex
                     direction={'column'}
                     justifyContent={'flex-start'}
                     gap={5}
                  >
                    
                  </Flex>
               </Flex>
            </Box>
         )}
      </>
   );
};

export default Dashboard;
