import image from '../images/integrations/shopify-connect.png';
import Card from './Card';
import endPoints from '../apis/agent';

import { useEffect, useState } from 'react';
import { useApiMutation, useApiQuery } from '../../../hooks/react-query-hooks';
import { channelNames } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import { connectDisconnectToShopifyConnect } from '../utils';
import { dialogMessage } from '../../../utils/strings/content-manager';
import { ApiError } from './facebook-ads-form';
import { useToast } from '@chakra-ui/react';
import { showConfirmationModal } from '../utils/modal-helpers';

const ShopifyConnect = () => {
   //replace nomencalture to shopify from shopifyconnect after app approval
   const [isDisconnecting, setIsDisconnecting] = useState(false);
   const toast = useToast();
   const authUser = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );

   const showToast = (
      title: string,
      description: string,
      status: 'success' | 'error',
   ) => {
      toast({ title, description, status, duration: 2000, isClosable: true });
   };
   // Fetch connection details
   const { data, isLoading, errorMessage } = useApiQuery({
      queryKey: [`shopifyConnectConnectionDetails`],
      queryFn: () =>
         endPoints.checkConnectionDetails({
            client_id: authUser?.client_id || '',
            channel_name: channelNames.SHOPIFY_CONNECT,
         }),
   });

   const { mutate: connectToSentiment, isPending: isConnectingToSentiment } =
      useApiMutation({
         mutationFn: connectDisconnectToShopifyConnect,
         onSuccessHandler: async (_data, payload) => {
            if (!payload?.isConnect) {
               setTimeout(() => {
                  window.location.href = `${window.location.origin}/integrations`;
               }, 1000);
               return;
            }
            const confirmed = await showConfirmationModal(
               dialogMessage.shopifyConnect.title,
               dialogMessage.shopifyConnect.description,

               {
                  icon: 'success',
                  confirmButtonText: 'OK',
                  showCancelButton: false,
               },
            );

            if (confirmed) {
               setTimeout(() => {
                  window.location.href = `${window.location.origin}/integrations`;
               }, 1000);
            }
         },
      });

   const { is_active = false } = data?.details || {};

   async function onConnect() {
      const {
         data: { url },
      } = await endPoints.getShopifyInstallUrl();
      window.location.href = url;
   }

   const onDisconnect = async () => {
      const result = await showConfirmationModal(
         dialogMessage.delete.title,
         dialogMessage.delete.description,
         {
            icon: 'warning',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, keep it',
            showCancelButton: true,
         },
      );
      if (result && authUser?.client_id) {
         try {
            setIsDisconnecting(true);
            connectToSentiment({
               channel_name: channelNames.SHOPIFY_CONNECT,
               client_id: authUser?.client_id,
               isConnect: false,
            });
         } catch (err) {
            const error = err as ApiError;
            const msg = error.response.data.message;
            showToast('Could not disconnect', msg!, 'error');
         } finally {
            setIsDisconnecting(false);
         }
      }
   };

   function onClick() {
      is_active ? void onDisconnect() : void onConnect();
   }

   useEffect(() => {
      const searchParams = new URLSearchParams(window.location.search);
      const shopify = searchParams.get('shopify');
      const shop = searchParams.get('shop');
      const accessToken = searchParams.get('t');

      if (shopify && shop && accessToken && authUser?.client_id) {
         connectToSentiment({
            isConnect: true,
            channel_name: channelNames.SHOPIFY_CONNECT,
            client_id: authUser.client_id,
            access_token: accessToken,
            store_url: shop,
         });
      }
   }, []);

   return (
      <Card
         error={errorMessage}
         isConnected={is_active}
         isDisconnecting={isDisconnecting}
         isConnecting={isConnectingToSentiment}
         isFetching={isLoading}
         onButtonClick={onClick}
         heading='Shopify'
         src={image}
      />
   );
};

export default ShopifyConnect;
