
import * as React from "react"
import { useDispatch } from "react-redux"
import { useAppSelector } from "../../../store/store"
import { setdateRange } from "../../../store/reducer/kpi-reducer"

import {
  format,
  addDays,
  
} from "date-fns"
import { Calendar } from "../../../components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "../../../components/ui/popover"
import { Button } from "../../../components/ui/button"
import { Calendar as CalendarIcon } from "lucide-react"
import { dateRanges } from "../utils/default-ranges"
import { DateRange } from "react-day-picker"
 


export default function DateRangeSelect() {
  const dispatch = useDispatch()
  const { dateRange } = useAppSelector((state) => state.kpi)
  const [selectedQuick, setSelectedQuick] = React.useState<string | null>(null)
  const [open, setOpen] = React.useState(false)

  const [range, setRange] = React.useState<DateRange | undefined>({
    from: new Date(dateRange.start),
    to: new Date(dateRange.end),
  })

 
  const applyRange = () => {
   
 if (!range?.from || !range?.to) return

  const newDateRange = {
    start: format(range.from, "yyyy-MM-dd'T'HH:mm:ss"),
    end: format(range.to, "yyyy-MM-dd'T'HH:mm:ss"),
  }

  
  if (
    newDateRange.start === dateRange.start &&
    newDateRange.end === dateRange.end
  ) {
   
    return
  }

  dispatch(setdateRange(newDateRange))
  
  }

 
  const selectPredefinedRange = (rangeConfig: typeof dateRanges[0]) => {
    const newRange = rangeConfig.getValue()
    setRange(newRange)
    setSelectedQuick(rangeConfig.label)
 
   
  }

  // Format display text
  const getDisplayText = () => {
    if (selectedQuick) return selectedQuick
    if (!range?.from) return "Select date range"
    if (!range?.to) return format(range.from, "MMM dd, yyyy")

    return `${format(range.from, "MMM dd")} - ${format(range.to, "MMM dd")}`
  }

  return (
    <Popover open={open} onOpenChange={(nextOpen) => {
    if (!nextOpen && range?.from && range?.to) {
      applyRange()   
    }
    setOpen(nextOpen)
  }}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          style={{
            width: '190px',
            justifyContent: 'flex-start',
            textAlign: 'left',
            fontWeight: 'normal',
            color: !range ? '#6b7280' : 'inherit'
          }}
         onClick={() => setOpen(true)}
        >
          <CalendarIcon className="mr-2 h-4 w-4" />
          {getDisplayText()}
        </Button>
      </PopoverTrigger>
      <PopoverContent style={{  padding: 0 , width: '440px',}} align="start">
        <div style={{ display: 'flex' }}>
          {/* Predefined ranges sidebar */}
          <div style={{ borderRight: '1px solid #e2e8f0',padding: '8px',        
  minWidth: '90px' }}>
            <div style={{ fontSize: '14px', fontWeight: '500', marginBottom: '8px' }}>Quick select</div>
            {dateRanges.map((rangeConfig) => (
              <Button
                key={rangeConfig.label}
                
    variant={selectedQuick === rangeConfig.label ? "default" : "ghost"}
                
                size="sm"
                  className={selectedQuick === rangeConfig.label 
    ? "bg-blue-500 text-white hover:bg-blue-600"   
    : ""}
                style={{ width: '100%', justifyContent: 'flex-start', fontSize: '14px', marginBottom: '4px',padding: '4px 6px'  }}
                onClick={() => selectPredefinedRange(rangeConfig)}
              >
                {rangeConfig.label}
              </Button>
            ))}
          </div>

          {/* Calendar */}
          <div style={{ padding: '6px' }}>
            <Calendar
              mode="range"
              defaultMonth={range?.from}
              selected={range}
             
              onSelect={(val) => {
    setRange(val)
    setSelectedQuick(null)  
  }}
              numberOfMonths={1}
              //className="w-[280px] h-[280px]"
              disabled={(date) =>
                date > new Date() || date < addDays(new Date(), -90)
              }
              modifiersClassNames={{
    range_start: "bg-blue-600 text-white", 
    range_end: "bg-blue-600 text-white",     
    range_middle: "bg-blue-200 text-blue-900" 
  }}
            />
            <div style={{ display: 'flex', justifyContent: 'flex-end', paddingTop: '12px', borderTop: '1px solid #e2e8f0', marginTop: '12px' }}>
              <div style={{ display: 'flex', gap: '8px' }}>
                

                <Button  size="sm"
  onClick={() => {
    applyRange()
    setOpen(false)  
  }}
  className="border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white">
                  Apply
                </Button>
              </div>
            </div>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  )
}
