import {
  addDays,
  endOfDay,
  startOfDay,
  startOfMonth,
  endOfMonth,
  addMonths,
  startOfWeek,
  endOfWeek,
  //isSameDay,
  startOfQuarter,
  endOfQuarter,
  startOfYear,
  endOfYear,
  //addMilliseconds,
  format,
} from "date-fns";

export const defineds = {
  startOfWeek: startOfWeek(new Date()),
  endOfWeek: endOfWeek(new Date()),
  startOfLastWeek: startOfWeek(addDays(new Date(), -7)),
  endOfLastWeek: endOfWeek(addDays(new Date(), -7)),
  startOfToday: new Date(
    format(startOfDay(new Date()), "yyyy-MM-dd'T'HH:mm:ss")
  ),
  endOfToday: new Date(format(endOfDay(new Date()), "yyyy-MM-dd'T'HH:mm:ss")),
  startOfYesterday: new Date(
    format(startOfDay(addDays(new Date(), -1)), "yyyy-MM-dd'T'HH:mm:ss")
  ),
  endOfYesterday: new Date(
    format(endOfDay(addDays(new Date(), -1)), "yyyy-MM-dd'T'HH:mm:ss")
  ),
  startOfMonth: startOfMonth(new Date()),
  endOfMonth: endOfMonth(new Date()),
  startOfLastMonth: startOfMonth(addMonths(new Date(), -1)),
  endOfLastMonth: endOfMonth(addMonths(new Date(), -1)),
  startOfQuarter: startOfQuarter(new Date()),
  endOfQuarter: endOfQuarter(new Date()),
  startOfYear: startOfYear(new Date()),
  endOfYear: endOfYear(new Date()),
  startOfLast7thDay: new Date(
    format(startOfDay(addDays(new Date(), -7)), "yyyy-MM-dd'T'HH:mm:ss")
  ),
  startOfLast30thDay: new Date(
    format(startOfDay(addDays(new Date(), -30)), "yyyy-MM-dd'T'HH:mm:ss")
  ),
  startOfLast60thDay: new Date(
    format(startOfDay(addDays(new Date(), -60)), "yyyy-MM-dd'T'HH:mm:ss")
  ),
  startOfLast90thDay: new Date(
    format(startOfDay(addDays(new Date(), -90)), "yyyy-MM-dd'T'HH:mm:ss")
  ),
  startOfLast180thDay: new Date(
    format(startOfDay(addDays(new Date(), -180)), "yyyy-MM-dd'T'HH:mm:ss")
  ),
};

export const dateRanges = [
  {
    label: "Today",
    getValue: () => ({
      from: startOfDay(new Date()),
      to: endOfDay(new Date()),
    }),
  },
  {
    label: "Yesterday",
    getValue: () => ({
      from: startOfDay(addDays(new Date(), -1)),
      to: endOfDay(addDays(new Date(), -1)),
    }),
  },
  {
    label: "Last 3 days",
    getValue: () => ({
      from: startOfDay(addDays(new Date(), -2)),
      to: endOfDay(new Date()),
    }),
  },
  {
    label: "Last 7 days",
    getValue: () => ({
      from: startOfDay(addDays(new Date(), -6)),
      to: endOfDay(new Date()),
    }),
  },

  {
    label: "Last 30 days",
    getValue: () => ({
      from: startOfDay(addDays(new Date(), -29)),
      to: endOfDay(new Date()),
    }),
  },

  {
    label: "Last 60 days",
    getValue: () => ({
      from: startOfDay(addDays(new Date(), -59)),
      to: endOfDay(new Date()),
    }),
  },
  {
    label: "Last 90 days",
    getValue: () => ({
      from: startOfDay(addDays(new Date(), -89)),
      to: endOfDay(new Date()),
    }),
  },
];
