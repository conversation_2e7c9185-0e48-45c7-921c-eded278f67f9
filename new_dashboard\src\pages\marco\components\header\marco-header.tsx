import { Button } from '@/components/ui/button';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { setCurrentSessionID as setAlertSessionID } from '@/store/reducer/alerting-agent-reducer';
import {
   setCurrentSessionID as setAnalyticsSessionID,
   setChunks,
   setDirectResponse,
} from '@/store/reducer/analytics-agent-reducer';
import { setCurrentAgent, Agents } from '@/store/reducer/marco-reducer';
import { useAppDispatch, useAppSelector } from '@/store/store';
import { useLocation, useNavigate } from 'react-router-dom';
import MarcoHistoryPopover from '../popovers/marco-history-popover';
import { resetMetaAdsManagerState } from '@/store/reducer/meta-ads-manager-reducer';
import { startNewAutoAgentChat } from '@/store/reducer/metaAdsAutoAgentReducer';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import Tooltips from '@/components/tooltip';
import { toast } from 'sonner';

const AGENTS: { value: Agents; label: string }[] = [
   { value: 'analytics-agent', label: 'Analytics Agent' },
   { value: 'alerting-agent', label: 'Alerting Agent' },
   { value: 'meta-ads-manager-agent', label: 'Meta Manager Agent' },
];

const MarcoHeader = () => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();
   const location = useLocation();

   const { currentAgent } = useAppSelector((state) => state.marco);
   const { directResponse, currentMode } = useAppSelector(
      (state) => state.analyticsAgent,
   );

   const AGENT_RESET_ACTIONS: Record<Agents, () => void> = {
      'analytics-agent': () => {
         dispatch(setAnalyticsSessionID(''));
         dispatch(setChunks([]));
      },
      'alerting-agent': () => dispatch(setAlertSessionID('')),
      'meta-ads-manager-agent': () => dispatch(resetMetaAdsManagerState()),
      'meta-ads-manager-auto': () => dispatch(startNewAutoAgentChat()),
   };

   const handleClickNewChat = () => {
      const resetAction = AGENT_RESET_ACTIONS[currentAgent];
      if (resetAction) resetAction();
   };

   return (
      <header className='flex mt-2 md:mt-4 w-full justify-between shrink-0 items-center gap-2 h-[52px]'>
         <div className='flex gap-2 mr-6'>
            <Select
               defaultValue={
                  currentAgent === 'meta-ads-manager-auto'
                     ? 'meta-ads-manager-agent'
                     : currentAgent
               }
               value={
                  currentAgent === 'meta-ads-manager-auto'
                     ? 'meta-ads-manager-agent'
                     : currentAgent
               }
               onValueChange={(value) => {
                  dispatch(setCurrentAgent(value as Agents));
                  navigate('/marco/' + value);
               }}
            >
               <SelectTrigger className='w-fit max-w-[220px] ml-2 text-[14px] md:text-[16px] text-black font-bold border-0 shadow-none hover:cursor-pointer'>
                  <SelectValue placeholder='Agent' />
               </SelectTrigger>
               <SelectContent className='bg-white'>
                  {AGENTS.map((agent) => (
                     <SelectItem
                        key={agent.value}
                        className='hover:cursor-pointer hover:bg-gray-50 font-semibold'
                        value={agent.value}
                     >
                        {agent.label}
                     </SelectItem>
                  ))}
               </SelectContent>
            </Select>
            {location.pathname === '/marco/analytics-agent' &&
               currentMode === 'cmo' && (
                  <Tooltips
                     className='bg-gray-600 text-white max-w-[400px]'
                     asChild={false}
                     content='When enabled, responses will stream in CMO mode, Diagnostics and Optimizations instead of running in the background. This does not affect the Data Analyst mode.'
                  >
                     <div className='flex items-center justify-between gap-2'>
                        <>
                           <Label
                              htmlFor='response-type'
                              className='font-semibold'
                           >
                              Streaming
                           </Label>
                           <Switch
                              id='response-type'
                              checked={!directResponse}
                              onCheckedChange={() => {
                                 if (directResponse) {
                                    toast.info(
                                       'Turning on streaming can sometimes cause timeout errors if the data is too large. It is recommended to turn off streaming when using CMO, Diagnostics or Optimizations.',
                                    );
                                 }
                                 dispatch(setDirectResponse(!directResponse));
                              }}
                           />
                        </>
                     </div>
                  </Tooltips>
               )}
         </div>
         <div className='flex gap-2 mr-6'>
            <Button
               className='text-[12px] md:text-[16px] text-black  hover:cursor-pointer hover:bg-[#3c76e1] hover:text-white disabled:cursor-not-allowed'
               variant='outline'
               onClick={handleClickNewChat}
            >
               New Chat
            </Button>
            <MarcoHistoryPopover />
         </div>
      </header>
   );
};

export default MarcoHeader;
