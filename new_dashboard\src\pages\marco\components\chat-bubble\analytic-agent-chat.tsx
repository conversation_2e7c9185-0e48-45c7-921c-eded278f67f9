import analyticAgentIcon from '../../../../assets/icons/analytics-agent.webp';
import StructuredResponse from '../../utils/analytics-agent/structured-response';
import {
   AnalyticsAgentChat,
   FetchSessionInsightsResponse,
   StreamChunk,
} from '@/api/service/agentic-workflow/analytics-agent';
import { SkeletonText, Spinner } from '@chakra-ui/react';
import {
   ERROR_MESSAGES,
   ListCircle,
} from '../../utils/analytics-agent/constants';
import { PiClock } from 'react-icons/pi';
import { AlertCircleIcon } from 'lucide-react';
import { Alert, AlertTitle } from '@/components/ui/alert';
import { PiCheckCircleFill } from 'react-icons/pi';
import {
   Accordion,
   AccordionItem,
   AccordionContent,
   AccordionTrigger,
} from '@/components/ui/accordion';
import { getChunkNodes } from '../../utils/analytics-agent/helpers';
import { useRef } from 'react';
import { useAppSelector } from '@/store/store';

const UserChatBubble = ({ content }: { content: string }) => {
   return (
      <div className='flex justify-end w-[100%] mt-2 mb-5'>
         <div className='w-[fit-content] max-w-[85%] md:max-w-[65%] bg-[#3444AE] text-white p-[12px_14px] text-left rounded-l-[15px] rounded-r-[0px] rounded-b-[15px] text-[15px] relative inline-block'>
            <p className='text-[14px] md:text-[16px]'>{content}</p>
         </div>
      </div>
   );
};

const ErrorChatBubble = () => {
   return (
      <div className='flex justify-start w-[100%] mt-2'>
         <div className='w-[100%] text-black p-[12px_14px]'>
            <div className='flex gap-6'>
               <img
                  src={analyticAgentIcon}
                  alt='Analytic Agent'
                  className='w-[40px] h-[40px] rounded-full'
               />
               <p className='text-md'>
                  {
                     ERROR_MESSAGES[
                        Math.floor(Math.random() * ERROR_MESSAGES.length)
                     ]
                  }
               </p>
            </div>
         </div>
      </div>
   );
};

const AbortChatBubble = () => {
   return (
      <div>
         <Alert variant='destructive' className=''>
            <AlertCircleIcon className='m-0 mb-2 text-red-500' />
            <AlertTitle className='mt-2 ml-2 text-bold text-red-600'>
               Something went wrong. Please try again later.
            </AlertTitle>
         </Alert>
      </div>
   );
};

const ChunkLoaderBubble = ({
   chunks,
   routedFrom,
}: {
   chunks: StreamChunk[];
   routedFrom:
      | 'diagnostic-agent'
      | 'diagnostic-agent/cmo'
      | 'diagnostic-agent/data-analyst'
      | 'alerting-agent'
      | null;
}) => {
   const { currentMode } = useAppSelector((state) => state.analyticsAgent);

   return (
      <div className='flex justify-start w-[100%] mt-2'>
         <div className='w-[100%] text-white p-[12px_14px]'>
            {!chunks ||
               (chunks?.length === 0 && (
                  <>
                     {' '}
                     <SkeletonText
                        noOfLines={3}
                        spacing='4'
                        width='100%'
                        startColor='gray.200'
                        endColor='gray.300'
                     />
                     {(currentMode == 'cmo' ||
                        routedFrom === 'diagnostic-agent') && (
                        <div className='flex flex-col items-start justify-center w-full m-2 gap-1 border-2 !border-blue-400 bg-blue-50 p-4 rounded-lg mt-4'>
                           <div className='flex items-center gap-2'>
                              <PiClock size={20} color='#3C76E1' />
                              <span className='text-md text-gray-700 font-bold'>
                                 Deep Analysis Initiated
                              </span>
                           </div>
                           <p className='text-sm text-gray-500 pl-[28px]'>
                              This complex query requires about{' '}
                              {routedFrom === 'diagnostic-agent/data-analyst'
                                 ? '8-10'
                                 : routedFrom === 'diagnostic-agent/cmo'
                                   ? '10-12'
                                   : currentMode === 'cmo'
                                     ? '8-10'
                                     : '8-10'}{' '}
                              minutes to complete. You can safely leave this
                              page. We'll notify you when it's ready.
                           </p>
                        </div>
                     )}
                  </>
               ))}
            {chunks?.length > 0 &&
               chunks.some((chunk) => chunk.type !== 'thought') && (
                  <>
                     <ol className='relative w-[95%] p-[-5px] text-gray-500 border-s !border-[#3C76E1] border-dashed dark:border-[!#4285F4] dark:text-gray-400'>
                        {chunks?.map((chunk: StreamChunk, index: number) => (
                           <>
                              <li className='mb-5 ml-5' key={index}>
                                 <span className='absolute flex items-center justify-center w-8 h-8 -start-4'>
                                    <PiCheckCircleFill
                                       size={20}
                                       style={{
                                          color: '#ffffff',
                                          fill: '#3C76E1',
                                          backgroundColor: '#ffffff',
                                       }}
                                    />
                                 </span>
                                 {chunk.type !== 'thought' && chunk.node}
                              </li>
                           </>
                        ))}
                     </ol>
                     <div className='flex items-center justify-start w-full h-[40px] ml-[-10px] mb-2 gap-2'>
                        <Spinner className='!border-gray-400' />
                        <p className='text-md w-full animate-pulse text-gray-400'>
                           Thinking...
                        </p>
                     </div>
                     {(currentMode == 'cmo' ||
                        routedFrom === 'diagnostic-agent') && (
                        <div className='flex flex-col items-start justify-center w-full m-2 gap-1 border-2 !border-blue-400 bg-blue-50 p-4 rounded-lg'>
                           <div className='flex items-center gap-2'>
                              <PiClock size={20} color='#3C76E1' />
                              <span className='text-md text-gray-700 font-bold'>
                                 Deep Analysis Initiated
                              </span>
                           </div>
                           <p className='text-sm text-gray-500 pl-[28px]'>
                              This complex query requires about{' '}
                              {routedFrom === 'diagnostic-agent/data-analyst'
                                 ? '8-10'
                                 : routedFrom === 'diagnostic-agent/cmo'
                                   ? '10-12'
                                   : currentMode === 'cmo'
                                     ? '8-10'
                                     : '8-10'}{' '}
                              minutes to complete. You can safely leave this
                              page. We'll notify you when it's ready.
                           </p>
                        </div>
                     )}
                  </>
               )}
            {chunks &&
               chunks.map(
                  (chunk) =>
                     chunk.type === 'thought' && (
                        <StructuredResponse
                           content={chunk?.content || ''}
                           loading={true}
                        />
                     ),
               )}
         </div>
      </div>
   );
};

const AgentChatBubble = ({
   currentChat,
   sessionInsights,
   responseTime,
   handleSendPrompt,
}: {
   currentChat: AnalyticsAgentChat;
   sessionInsights: FetchSessionInsightsResponse[];
   responseTime: number;
   handleSendPrompt: (
      userVisiblePrompt?: string,
      actualAiPrompt?: string,
   ) => Promise<void>;
}) => {
   const thoughtRef = useRef<HTMLDivElement>(null);

   const currentChatInsights = sessionInsights?.find(
      (insight) => insight.chat_id === currentChat.chat_id,
   )?.chat_flow_context;

   let chunkNodes: {
      type:
         | 'tool_call'
         | 'tool_input'
         | 'transition'
         | 'thought'
         | 'final_result';
      node: JSX.Element;
   }[] = [];

   if (currentChatInsights) {
      chunkNodes = getChunkNodes(currentChatInsights);
   }

   const handleAccordionToggle = () => {
      setTimeout(() => {
         thoughtRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
         });
      }, 300);
   };

   return (
      <div className='flex justify-start w-[100%] mt-2'>
         <div className='w-[100%] text-black p-[12px_14px]'>
            <div className='flex flex-col'>
               <Accordion type='single' collapsible>
                  <AccordionItem value='item-1'>
                     <AccordionTrigger className='text-gray-500 text-[16px] justify-start gap-1 hover:cursor-pointer [&[data-state=open]>svg]:rotate-90 hover:no-underline'>
                        <div
                           className='text-[12px] md:text-[16px]'
                           onClick={handleAccordionToggle}
                           ref={thoughtRef}
                        >
                           Thought for {(responseTime / 1000).toFixed(0)}s
                        </div>
                     </AccordionTrigger>
                     <AccordionContent className='pl-[10px]'>
                        <ol className='relative w-[95%] text-gray-500 border-s border-gray-200 dark:border-gray-700 dark:text-gray-400'>
                           {chunkNodes?.map((chunk, index) => (
                              <li className='mb-5 ml-5' key={index}>
                                 <span className='absolute flex items-center justify-center w-8 h-8 -start-4'>
                                    <ListCircle />
                                 </span>
                                 {chunk.node}
                              </li>
                           ))}
                        </ol>
                     </AccordionContent>
                  </AccordionItem>
               </Accordion>
               <StructuredResponse
                  query={currentChat.user_query}
                  content={currentChat.final_response}
                  currentChat={currentChat}
                  finalInsight={
                     currentChatInsights?.find(
                        (insight) => insight.status === 'completed',
                     ) || null
                  }
                  handleSendPrompt={handleSendPrompt}
               />
               <div>
                  {currentChat.response_status === 'aborted' && (
                     <AbortChatBubble />
                  )}
               </div>
            </div>
         </div>
      </div>
   );
};

const AnalyticAgentChat = (props: {
   chunks: StreamChunk[];
   sessionInsights: FetchSessionInsightsResponse[];
   currentSessionChats: AnalyticsAgentChat[];
   handleSendPrompt: (
      userVisiblePrompt?: string,
      actualAiPrompt?: string,
   ) => Promise<void>;
   routedFrom:
      | 'diagnostic-agent'
      | 'diagnostic-agent/cmo'
      | 'diagnostic-agent/data-analyst'
      | 'alerting-agent'
      | null;
}) => {
   const {
      chunks,
      sessionInsights,
      currentSessionChats,
      handleSendPrompt,
      routedFrom,
   } = props;

   return (
      <>
         {currentSessionChats?.map((currentChat) => (
            <div key={currentChat.chat_id}>
               <UserChatBubble content={currentChat.user_query} />
               {currentChat.response_status === 'error' ? (
                  <ErrorChatBubble />
               ) : currentChat.response_status === 'pending' ||
                 currentChat.response_status === 'running' ? (
                  <ChunkLoaderBubble chunks={chunks} routedFrom={routedFrom} />
               ) : (
                  <AgentChatBubble
                     currentChat={currentChat}
                     sessionInsights={sessionInsights}
                     responseTime={currentChat.response_time}
                     handleSendPrompt={handleSendPrompt}
                  />
               )}
            </div>
         ))}
      </>
   );
};

export default AnalyticAgentChat;
